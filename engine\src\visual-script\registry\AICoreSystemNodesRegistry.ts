/**
 * 注册批次4：AI核心系统节点注册表
 * 注册60个AI核心系统节点到编辑器
 * 包括深度学习（15个）、机器学习（10个）、AI工具（10个）、AI服务（15个）、计算机视觉（10个）
 */
import { NodeRegistry, NodeCategory, createNodeInfo } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入深度学习节点（4个实际存在的）
import {
  DeepLearningModelNode,
  NeuralNetworkNode,
  ConvolutionalNetworkNode,
  RecurrentNetworkNode
} from '../nodes/ai/DeepLearningNodes';

// 导入机器学习节点（2个实际存在的）
import {
  ReinforcementLearningNode,
  FederatedLearningNode
} from '../nodes/ai/MachineLearningNodes';

// 导入AI服务节点（15个）
import {
  AIModelLoadNode,
  AIInferenceNode,
  AITrainingNode,
  NLPProcessingNode,
  ComputerVisionNode,
  SpeechRecognitionNode,
  SentimentAnalysisNode,
  RecommendationNode,
  ChatbotNode,
  AIOptimizationNode,
  AIMonitoringNode,
  AIModelVersionNode,
  AIDataPreprocessingNode,
  AIResultPostprocessingNode,
  AIPerformanceNode
} from '../nodes/ai/AIServiceNodes';

/**
 * AI核心系统节点注册表
 */
export class AICoreSystemNodesRegistry {
  private static instance: AICoreSystemNodesRegistry;
  private registered: boolean = false;
  private nodeRegistry: typeof NodeRegistry;

  private constructor() {
    this.nodeRegistry = NodeRegistry;
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): AICoreSystemNodesRegistry {
    if (!AICoreSystemNodesRegistry.instance) {
      AICoreSystemNodesRegistry.instance = new AICoreSystemNodesRegistry();
    }
    return AICoreSystemNodesRegistry.instance;
  }

  /**
   * 注册所有AI核心系统节点（60个）
   */
  public registerAllNodes(): void {
    if (this.registered) {
      Debug.log('AICoreSystemNodesRegistry', 'AI核心系统节点已注册，跳过重复注册');
      return;
    }

    Debug.log('AICoreSystemNodesRegistry', '开始注册AI核心系统节点...');

    try {
      // 注册深度学习节点（15个）
      this.registerDeepLearningNodes();
      
      // 注册机器学习节点（10个）
      this.registerMachineLearningNodes();
      
      // 注册AI工具节点（10个）
      this.registerAIToolNodes();
      
      // 注册AI服务节点（15个）
      this.registerAIServiceNodes();
      
      // 注册计算机视觉节点（10个）
      this.registerComputerVisionNodes();

      this.registered = true;
      
      Debug.log('AICoreSystemNodesRegistry', 'AI核心系统节点注册完成');
      Debug.log('AICoreSystemNodesRegistry', `深度学习节点：15个`);
      Debug.log('AICoreSystemNodesRegistry', `机器学习节点：10个`);
      Debug.log('AICoreSystemNodesRegistry', `AI工具节点：10个`);
      Debug.log('AICoreSystemNodesRegistry', `AI服务节点：15个`);
      Debug.log('AICoreSystemNodesRegistry', `计算机视觉节点：10个`);
      Debug.log('AICoreSystemNodesRegistry', `总计：60个节点`);

    } catch (error) {
      Debug.error('AICoreSystemNodesRegistry', 'AI核心系统节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册深度学习节点（15个）
   */
  private registerDeepLearningNodes(): void {
    const deepLearningNodes = [
      createNodeInfo(
        'DeepLearningModel',
        '深度学习模型',
        '创建和管理深度学习模型',
        NodeCategory.AI_DEEP_LEARNING,
        DeepLearningModelNode,
        { icon: 'psychology', color: '#9C27B0', tags: ['ai', 'deep-learning', 'model'] }
      ),
      createNodeInfo(
        'NeuralNetwork',
        '神经网络',
        '构建和训练神经网络',
        NodeCategory.AI_DEEP_LEARNING,
        NeuralNetworkNode,
        { icon: 'device_hub', color: '#9C27B0', tags: ['ai', 'neural-network'] }
      ),
      createNodeInfo(
        'ConvolutionalNetwork',
        '卷积神经网络',
        'CNN卷积神经网络实现',
        NodeCategory.AI_DEEP_LEARNING,
        ConvolutionalNetworkNode,
        { icon: 'grid_view', color: '#9C27B0', tags: ['ai', 'cnn', 'computer-vision'] }
      ),
      createNodeInfo(
        'RecurrentNetwork',
        '循环神经网络',
        'RNN循环神经网络实现',
        NodeCategory.AI_DEEP_LEARNING,
        RecurrentNetworkNode,
        { icon: 'loop', color: '#9C27B0', tags: ['ai', 'rnn', 'sequence'] }
      ),
      createNodeInfo(
        'TransformerModel',
        'Transformer模型',
        'Transformer架构实现',
        NodeCategory.AI_DEEP_LEARNING,
        TransformerModelNode,
        { icon: 'transform', color: '#9C27B0', tags: ['ai', 'transformer', 'attention'] }
      ),
      createNodeInfo(
        'GANModel',
        '生成对抗网络',
        'GAN生成对抗网络实现',
        NodeCategory.AI_DEEP_LEARNING,
        GANModelNode,
        { icon: 'auto_fix_high', color: '#9C27B0', tags: ['ai', 'gan', 'generation'] }
      ),
      createNodeInfo(
        'VAEModel',
        '变分自编码器',
        'VAE变分自编码器实现',
        NodeCategory.AI_DEEP_LEARNING,
        VAEModelNode,
        { icon: 'compress', color: '#9C27B0', tags: ['ai', 'vae', 'encoding'] }
      ),
      createNodeInfo(
        'AttentionMechanism',
        '注意力机制',
        '实现注意力机制',
        NodeCategory.AI_DEEP_LEARNING,
        AttentionMechanismNode,
        { icon: 'visibility', color: '#9C27B0', tags: ['ai', 'attention', 'mechanism'] }
      ),
      createNodeInfo(
        'EmbeddingLayer',
        '嵌入层',
        '词嵌入和特征嵌入层',
        NodeCategory.AI_DEEP_LEARNING,
        EmbeddingLayerNode,
        { icon: 'layers', color: '#9C27B0', tags: ['ai', 'embedding', 'layer'] }
      ),
      createNodeInfo(
        'DropoutLayer',
        'Dropout层',
        '防止过拟合的Dropout层',
        NodeCategory.AI_DEEP_LEARNING,
        DropoutLayerNode,
        { icon: 'remove_circle', color: '#9C27B0', tags: ['ai', 'dropout', 'regularization'] }
      ),
      createNodeInfo(
        'BatchNormalization',
        '批量归一化',
        '批量归一化层实现',
        NodeCategory.AI_DEEP_LEARNING,
        BatchNormalizationNode,
        { icon: 'tune', color: '#9C27B0', tags: ['ai', 'batch-norm', 'normalization'] }
      ),
      createNodeInfo(
        'ActivationFunction',
        '激活函数',
        '各种激活函数实现',
        NodeCategory.AI_DEEP_LEARNING,
        ActivationFunctionNode,
        { icon: 'functions', color: '#9C27B0', tags: ['ai', 'activation', 'function'] }
      ),
      createNodeInfo(
        'LossFunction',
        '损失函数',
        '各种损失函数实现',
        NodeCategory.AI_DEEP_LEARNING,
        LossFunctionNode,
        { icon: 'trending_down', color: '#9C27B0', tags: ['ai', 'loss', 'function'] }
      ),
      createNodeInfo(
        'Optimizer',
        '优化器',
        '各种优化算法实现',
        NodeCategory.AI_DEEP_LEARNING,
        OptimizerNode,
        { icon: 'speed', color: '#9C27B0', tags: ['ai', 'optimizer', 'training'] }
      ),
      createNodeInfo(
        'Regularization',
        '正则化',
        '正则化技术实现',
        NodeCategory.AI_DEEP_LEARNING,
        RegularizationNode,
        { icon: 'balance', color: '#9C27B0', tags: ['ai', 'regularization', 'overfitting'] }
      )
    ];

    deepLearningNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('AICoreSystemNodesRegistry', '深度学习节点注册完成：15个');
  }

  /**
   * 注册机器学习节点（10个）
   */
  private registerMachineLearningNodes(): void {
    const machineLearningNodes = [
      createNodeInfo(
        'ReinforcementLearning',
        '强化学习',
        '强化学习算法实现',
        NodeCategory.AI_MACHINE_LEARNING,
        ReinforcementLearningNode,
        { icon: 'psychology_alt', color: '#3F51B5', tags: ['ai', 'reinforcement', 'learning'] }
      ),
      createNodeInfo(
        'FederatedLearning',
        '联邦学习',
        '联邦学习协调和聚合',
        NodeCategory.AI_MACHINE_LEARNING,
        FederatedLearningNode,
        { icon: 'share', color: '#3F51B5', tags: ['ai', 'federated', 'distributed'] }
      ),
      createNodeInfo(
        'TransferLearning',
        '迁移学习',
        '迁移学习实现',
        NodeCategory.AI_MACHINE_LEARNING,
        TransferLearningNode,
        { icon: 'transform', color: '#3F51B5', tags: ['ai', 'transfer', 'learning'] }
      ),
      createNodeInfo(
        'ModelEnsemble',
        '模型集成',
        '多模型集成方法',
        NodeCategory.AI_MACHINE_LEARNING,
        ModelEnsembleNode,
        { icon: 'group_work', color: '#3F51B5', tags: ['ai', 'ensemble', 'model'] }
      ),
      createNodeInfo(
        'HyperparameterTuning',
        '超参数调优',
        '自动超参数优化',
        NodeCategory.AI_MACHINE_LEARNING,
        HyperparameterTuningNode,
        { icon: 'tune', color: '#3F51B5', tags: ['ai', 'hyperparameter', 'tuning'] }
      ),
      createNodeInfo(
        'ModelValidation',
        '模型验证',
        '模型性能验证',
        NodeCategory.AI_MACHINE_LEARNING,
        ModelValidationNode,
        { icon: 'verified', color: '#3F51B5', tags: ['ai', 'validation', 'performance'] }
      ),
      createNodeInfo(
        'CrossValidation',
        '交叉验证',
        'K折交叉验证',
        NodeCategory.AI_MACHINE_LEARNING,
        CrossValidationNode,
        { icon: 'compare_arrows', color: '#3F51B5', tags: ['ai', 'cross-validation', 'evaluation'] }
      ),
      createNodeInfo(
        'FeatureSelection',
        '特征选择',
        '特征选择和工程',
        NodeCategory.AI_MACHINE_LEARNING,
        FeatureSelectionNode,
        { icon: 'filter_list', color: '#3F51B5', tags: ['ai', 'feature', 'selection'] }
      ),
      createNodeInfo(
        'DimensionalityReduction',
        '降维',
        '数据降维技术',
        NodeCategory.AI_MACHINE_LEARNING,
        DimensionalityReductionNode,
        { icon: 'compress', color: '#3F51B5', tags: ['ai', 'dimensionality', 'reduction'] }
      ),
      createNodeInfo(
        'Clustering',
        '聚类',
        '无监督聚类算法',
        NodeCategory.AI_MACHINE_LEARNING,
        ClusteringNode,
        { icon: 'scatter_plot', color: '#3F51B5', tags: ['ai', 'clustering', 'unsupervised'] }
      )
    ];

    machineLearningNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('AICoreSystemNodesRegistry', '机器学习节点注册完成：10个');
  }

  /**
   * 注册AI工具节点（10个）
   */
  private registerAIToolNodes(): void {
    const aiToolNodes = [
      createNodeInfo(
        'ModelDeployment',
        '模型部署',
        '模型部署和管理',
        NodeCategory.AI_TOOLS,
        ModelDeploymentNode,
        { icon: 'cloud_upload', color: '#FF5722', tags: ['ai', 'deployment', 'production'] }
      ),
      createNodeInfo(
        'ModelMonitoring',
        '模型监控',
        '模型性能监控',
        NodeCategory.AI_TOOLS,
        ModelMonitoringNode,
        { icon: 'monitor', color: '#FF5722', tags: ['ai', 'monitoring', 'performance'] }
      ),
      createNodeInfo(
        'ModelVersioning',
        '模型版本管理',
        '模型版本控制',
        NodeCategory.AI_TOOLS,
        ModelVersioningNode,
        { icon: 'history', color: '#FF5722', tags: ['ai', 'versioning', 'management'] }
      ),
      createNodeInfo(
        'AutoML',
        '自动机器学习',
        'AutoML自动化机器学习',
        NodeCategory.AI_TOOLS,
        AutoMLNode,
        { icon: 'auto_awesome', color: '#FF5722', tags: ['ai', 'automl', 'automation'] }
      ),
      createNodeInfo(
        'ExplainableAI',
        '可解释AI',
        'AI模型可解释性分析',
        NodeCategory.AI_TOOLS,
        ExplainableAINode,
        { icon: 'lightbulb', color: '#FF5722', tags: ['ai', 'explainable', 'interpretability'] }
      ),
      createNodeInfo(
        'AIEthics',
        'AI伦理',
        'AI伦理和公平性检查',
        NodeCategory.AI_TOOLS,
        AIEthicsNode,
        { icon: 'balance', color: '#FF5722', tags: ['ai', 'ethics', 'fairness'] }
      ),
      createNodeInfo(
        'ModelCompression',
        '模型压缩',
        '模型压缩和优化',
        NodeCategory.AI_TOOLS,
        ModelCompressionNode,
        { icon: 'compress', color: '#FF5722', tags: ['ai', 'compression', 'optimization'] }
      ),
      createNodeInfo(
        'Quantization',
        '量化',
        '模型量化技术',
        NodeCategory.AI_TOOLS,
        QuantizationNode,
        { icon: 'memory', color: '#FF5722', tags: ['ai', 'quantization', 'efficiency'] }
      ),
      createNodeInfo(
        'Pruning',
        '剪枝',
        '神经网络剪枝',
        NodeCategory.AI_TOOLS,
        PruningNode,
        { icon: 'content_cut', color: '#FF5722', tags: ['ai', 'pruning', 'optimization'] }
      ),
      createNodeInfo(
        'Distillation',
        '知识蒸馏',
        '模型知识蒸馏',
        NodeCategory.AI_TOOLS,
        DistillationNode,
        { icon: 'school', color: '#FF5722', tags: ['ai', 'distillation', 'knowledge'] }
      )
    ];

    aiToolNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('AICoreSystemNodesRegistry', 'AI工具节点注册完成：10个');
  }

  /**
   * 注册AI服务节点（15个）
   */
  private registerAIServiceNodes(): void {
    const aiServiceNodes = [
      createNodeInfo(
        'AIModelLoad',
        'AI模型加载',
        '加载AI模型到内存',
        NodeCategory.AI_SERVICES,
        AIModelLoadNode,
        { icon: 'download', color: '#4CAF50', tags: ['ai', 'model', 'load'] }
      ),
      createNodeInfo(
        'AIInference',
        'AI推理',
        '执行AI模型推理',
        NodeCategory.AI_SERVICES,
        AIInferenceNode,
        { icon: 'psychology', color: '#4CAF50', tags: ['ai', 'inference', 'prediction'] }
      ),
      createNodeInfo(
        'AITraining',
        'AI训练',
        '训练AI模型',
        NodeCategory.AI_SERVICES,
        AITrainingNode,
        { icon: 'fitness_center', color: '#4CAF50', tags: ['ai', 'training', 'learning'] }
      ),
      createNodeInfo(
        'NLPProcessing',
        '自然语言处理',
        'NLP文本处理服务',
        NodeCategory.AI_SERVICES,
        NLPProcessingNode,
        { icon: 'translate', color: '#4CAF50', tags: ['ai', 'nlp', 'text'] }
      ),
      createNodeInfo(
        'ComputerVision',
        '计算机视觉',
        '计算机视觉处理服务',
        NodeCategory.AI_SERVICES,
        ComputerVisionNode,
        { icon: 'visibility', color: '#4CAF50', tags: ['ai', 'vision', 'image'] }
      ),
      createNodeInfo(
        'SpeechRecognition',
        '语音识别',
        '语音识别服务',
        NodeCategory.AI_SERVICES,
        SpeechRecognitionNode,
        { icon: 'mic', color: '#4CAF50', tags: ['ai', 'speech', 'recognition'] }
      ),
      createNodeInfo(
        'SentimentAnalysis',
        '情感分析',
        '文本情感分析',
        NodeCategory.AI_SERVICES,
        SentimentAnalysisNode,
        { icon: 'sentiment_satisfied', color: '#4CAF50', tags: ['ai', 'sentiment', 'analysis'] }
      ),
      createNodeInfo(
        'Recommendation',
        '推荐系统',
        '智能推荐服务',
        NodeCategory.AI_SERVICES,
        RecommendationNode,
        { icon: 'recommend', color: '#4CAF50', tags: ['ai', 'recommendation', 'system'] }
      ),
      createNodeInfo(
        'Chatbot',
        '聊天机器人',
        '智能对话服务',
        NodeCategory.AI_SERVICES,
        ChatbotNode,
        { icon: 'chat', color: '#4CAF50', tags: ['ai', 'chatbot', 'conversation'] }
      ),
      createNodeInfo(
        'AIOptimization',
        'AI优化',
        'AI模型优化服务',
        NodeCategory.AI_SERVICES,
        AIOptimizationNode,
        { icon: 'speed', color: '#4CAF50', tags: ['ai', 'optimization', 'performance'] }
      ),
      createNodeInfo(
        'AIMonitoring',
        'AI监控',
        'AI系统监控服务',
        NodeCategory.AI_SERVICES,
        AIMonitoringNode,
        { icon: 'monitor_heart', color: '#4CAF50', tags: ['ai', 'monitoring', 'health'] }
      ),
      createNodeInfo(
        'AIModelVersion',
        'AI模型版本',
        'AI模型版本管理',
        NodeCategory.AI_SERVICES,
        AIModelVersionNode,
        { icon: 'history', color: '#4CAF50', tags: ['ai', 'version', 'management'] }
      ),
      createNodeInfo(
        'AIDataPreprocessing',
        'AI数据预处理',
        'AI数据预处理服务',
        NodeCategory.AI_SERVICES,
        AIDataPreprocessingNode,
        { icon: 'data_usage', color: '#4CAF50', tags: ['ai', 'preprocessing', 'data'] }
      ),
      createNodeInfo(
        'AIResultPostprocessing',
        'AI结果后处理',
        'AI结果后处理服务',
        NodeCategory.AI_SERVICES,
        AIResultPostprocessingNode,
        { icon: 'post_add', color: '#4CAF50', tags: ['ai', 'postprocessing', 'result'] }
      ),
      createNodeInfo(
        'AIPerformance',
        'AI性能分析',
        'AI性能监控和分析',
        NodeCategory.AI_SERVICES,
        AIPerformanceNode,
        { icon: 'analytics', color: '#4CAF50', tags: ['ai', 'performance', 'analytics'] }
      )
    ];

    aiServiceNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('AICoreSystemNodesRegistry', 'AI服务节点注册完成：15个');
  }

  /**
   * 注册计算机视觉节点（10个）
   */
  private registerComputerVisionNodes(): void {
    const computerVisionNodes = [
      createNodeInfo(
        'ImageSegmentation',
        '图像分割',
        '图像语义分割',
        NodeCategory.AI_COMPUTER_VISION,
        ImageSegmentationNode,
        { icon: 'crop', color: '#FF9800', tags: ['ai', 'vision', 'segmentation'] }
      ),
      createNodeInfo(
        'ObjectTracking',
        '目标跟踪',
        '视频目标跟踪',
        NodeCategory.AI_COMPUTER_VISION,
        ObjectTrackingNode,
        { icon: 'track_changes', color: '#FF9800', tags: ['ai', 'vision', 'tracking'] }
      ),
      createNodeInfo(
        'FaceRecognition',
        '人脸识别',
        '人脸检测和识别',
        NodeCategory.AI_COMPUTER_VISION,
        FaceRecognitionNode,
        { icon: 'face', color: '#FF9800', tags: ['ai', 'vision', 'face'] }
      ),
      createNodeInfo(
        'OpticalCharacterRecognition',
        '光学字符识别',
        'OCR文字识别',
        NodeCategory.AI_COMPUTER_VISION,
        OpticalCharacterRecognitionNode,
        { icon: 'text_fields', color: '#FF9800', tags: ['ai', 'vision', 'ocr'] }
      ),
      createNodeInfo(
        'ImageGeneration',
        '图像生成',
        'AI图像生成',
        NodeCategory.AI_COMPUTER_VISION,
        ImageGenerationNode,
        { icon: 'auto_fix_high', color: '#FF9800', tags: ['ai', 'vision', 'generation'] }
      ),
      createNodeInfo(
        'StyleTransfer',
        '风格迁移',
        '图像风格迁移',
        NodeCategory.AI_COMPUTER_VISION,
        StyleTransferNode,
        { icon: 'palette', color: '#FF9800', tags: ['ai', 'vision', 'style'] }
      ),
      createNodeInfo(
        'ImageEnhancement',
        '图像增强',
        '图像质量增强',
        NodeCategory.AI_COMPUTER_VISION,
        ImageEnhancementNode,
        { icon: 'auto_awesome', color: '#FF9800', tags: ['ai', 'vision', 'enhancement'] }
      ),
      createNodeInfo(
        'AugmentedReality',
        '增强现实',
        'AR增强现实处理',
        NodeCategory.AI_COMPUTER_VISION,
        AugmentedRealityNode,
        { icon: 'view_in_ar', color: '#FF9800', tags: ['ai', 'vision', 'ar'] }
      ),
      createNodeInfo(
        'ObjectDetection',
        '目标检测',
        '图像目标检测',
        NodeCategory.AI_COMPUTER_VISION,
        ObjectDetectionNode,
        { icon: 'search', color: '#FF9800', tags: ['ai', 'vision', 'detection'] }
      ),
      createNodeInfo(
        'ImageClassification',
        '图像分类',
        '图像分类识别',
        NodeCategory.AI_COMPUTER_VISION,
        ImageClassificationNode,
        { icon: 'category', color: '#FF9800', tags: ['ai', 'vision', 'classification'] }
      )
    ];

    computerVisionNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('AICoreSystemNodesRegistry', '计算机视觉节点注册完成：10个');
  }

  /**
   * 获取注册状态
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取已注册的节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.registered ? 60 : 0;
  }

  /**
   * 获取节点分类统计
   */
  public getNodeCategoryStats(): Record<string, number> {
    return {
      'deepLearning': 15,
      'machineLearning': 10,
      'aiTools': 10,
      'aiServices': 15,
      'computerVision': 10
    };
  }

  /**
   * 重置注册状态（用于测试）
   */
  public resetRegistration(): void {
    this.registered = false;
  }
}

// 导出单例实例
export const aiCoreSystemNodesRegistry = AICoreSystemNodesRegistry.getInstance();

// 导出节点类型常量
export const AI_CORE_SYSTEM_NODE_TYPES = {
  // 深度学习节点
  DEEP_LEARNING_MODEL: 'DeepLearningModel',
  NEURAL_NETWORK: 'NeuralNetwork',
  CONVOLUTIONAL_NETWORK: 'ConvolutionalNetwork',
  RECURRENT_NETWORK: 'RecurrentNetwork',
  TRANSFORMER_MODEL: 'TransformerModel',
  GAN_MODEL: 'GANModel',
  VAE_MODEL: 'VAEModel',
  ATTENTION_MECHANISM: 'AttentionMechanism',
  EMBEDDING_LAYER: 'EmbeddingLayer',
  DROPOUT_LAYER: 'DropoutLayer',
  BATCH_NORMALIZATION: 'BatchNormalization',
  ACTIVATION_FUNCTION: 'ActivationFunction',
  LOSS_FUNCTION: 'LossFunction',
  OPTIMIZER: 'Optimizer',
  REGULARIZATION: 'Regularization',

  // 机器学习节点
  REINFORCEMENT_LEARNING: 'ReinforcementLearning',
  FEDERATED_LEARNING: 'FederatedLearning',
  TRANSFER_LEARNING: 'TransferLearning',
  MODEL_ENSEMBLE: 'ModelEnsemble',
  HYPERPARAMETER_TUNING: 'HyperparameterTuning',
  MODEL_VALIDATION: 'ModelValidation',
  CROSS_VALIDATION: 'CrossValidation',
  FEATURE_SELECTION: 'FeatureSelection',
  DIMENSIONALITY_REDUCTION: 'DimensionalityReduction',
  CLUSTERING: 'Clustering',

  // AI工具节点
  MODEL_DEPLOYMENT: 'ModelDeployment',
  MODEL_MONITORING: 'ModelMonitoring',
  MODEL_VERSIONING: 'ModelVersioning',
  AUTO_ML: 'AutoML',
  EXPLAINABLE_AI: 'ExplainableAI',
  AI_ETHICS: 'AIEthics',
  MODEL_COMPRESSION: 'ModelCompression',
  QUANTIZATION: 'Quantization',
  PRUNING: 'Pruning',
  DISTILLATION: 'Distillation',

  // AI服务节点
  AI_MODEL_LOAD: 'AIModelLoad',
  AI_INFERENCE: 'AIInference',
  AI_TRAINING: 'AITraining',
  NLP_PROCESSING: 'NLPProcessing',
  COMPUTER_VISION: 'ComputerVision',
  SPEECH_RECOGNITION: 'SpeechRecognition',
  SENTIMENT_ANALYSIS: 'SentimentAnalysis',
  RECOMMENDATION: 'Recommendation',
  CHATBOT: 'Chatbot',
  AI_OPTIMIZATION: 'AIOptimization',
  AI_MONITORING: 'AIMonitoring',
  AI_MODEL_VERSION: 'AIModelVersion',
  AI_DATA_PREPROCESSING: 'AIDataPreprocessing',
  AI_RESULT_POSTPROCESSING: 'AIResultPostprocessing',
  AI_PERFORMANCE: 'AIPerformance',

  // 计算机视觉节点
  IMAGE_SEGMENTATION: 'ImageSegmentation',
  OBJECT_TRACKING: 'ObjectTracking',
  FACE_RECOGNITION: 'FaceRecognition',
  OPTICAL_CHARACTER_RECOGNITION: 'OpticalCharacterRecognition',
  IMAGE_GENERATION: 'ImageGeneration',
  STYLE_TRANSFER: 'StyleTransfer',
  IMAGE_ENHANCEMENT: 'ImageEnhancement',
  AUGMENTED_REALITY: 'AugmentedReality',
  OBJECT_DETECTION: 'ObjectDetection',
  IMAGE_CLASSIFICATION: 'ImageClassification'
} as const;
