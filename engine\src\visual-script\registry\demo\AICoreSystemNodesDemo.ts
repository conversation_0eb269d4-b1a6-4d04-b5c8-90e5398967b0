/**
 * AI核心系统节点注册表演示
 * 展示如何使用AI核心系统节点注册表
 */

import { aiCoreSystemNodesRegistry, AI_CORE_SYSTEM_NODE_TYPES } from '../AICoreSystemNodesRegistry';
import { NodeRegistry } from '../NodeRegistry';
import { Debug } from '../../../utils/Debug';

/**
 * AI核心系统节点注册演示
 */
export class AICoreSystemNodesDemo {
  
  /**
   * 运行完整演示
   */
  public static async runDemo(): Promise<void> {
    Debug.log('AICoreSystemNodesDemo', '开始AI核心系统节点注册演示...');

    try {
      // 1. 演示注册表初始化
      await this.demonstrateRegistryInitialization();
      
      // 2. 演示节点注册
      await this.demonstrateNodeRegistration();
      
      // 3. 演示节点查找和创建
      await this.demonstrateNodeUsage();
      
      // 4. 演示统计信息
      await this.demonstrateStatistics();
      
      // 5. 演示分类查询
      await this.demonstrateCategoryQueries();

      Debug.log('AICoreSystemNodesDemo', 'AI核心系统节点注册演示完成！');
      
    } catch (error) {
      Debug.error('AICoreSystemNodesDemo', '演示过程中发生错误:', error);
      throw error;
    }
  }

  /**
   * 演示注册表初始化
   */
  private static async demonstrateRegistryInitialization(): Promise<void> {
    Debug.log('AICoreSystemNodesDemo', '=== 1. 注册表初始化演示 ===');
    
    // 检查初始状态
    Debug.log('AICoreSystemNodesDemo', `注册前状态: 已注册=${aiCoreSystemNodesRegistry.isRegistered()}, 节点数=${aiCoreSystemNodesRegistry.getRegisteredNodeCount()}`);
    
    // 重置注册状态（仅用于演示）
    aiCoreSystemNodesRegistry.resetRegistration();
    Debug.log('AICoreSystemNodesDemo', `重置后状态: 已注册=${aiCoreSystemNodesRegistry.isRegistered()}, 节点数=${aiCoreSystemNodesRegistry.getRegisteredNodeCount()}`);
  }

  /**
   * 演示节点注册
   */
  private static async demonstrateNodeRegistration(): Promise<void> {
    Debug.log('AICoreSystemNodesDemo', '=== 2. 节点注册演示 ===');
    
    const startTime = Date.now();
    
    // 注册所有AI核心系统节点
    aiCoreSystemNodesRegistry.registerAllNodes();
    
    const endTime = Date.now();
    const registrationTime = endTime - startTime;
    
    Debug.log('AICoreSystemNodesDemo', `注册完成: 耗时${registrationTime}ms`);
    Debug.log('AICoreSystemNodesDemo', `注册后状态: 已注册=${aiCoreSystemNodesRegistry.isRegistered()}, 节点数=${aiCoreSystemNodesRegistry.getRegisteredNodeCount()}`);
    
    // 测试重复注册
    const startTime2 = Date.now();
    aiCoreSystemNodesRegistry.registerAllNodes();
    const endTime2 = Date.now();
    const duplicateRegistrationTime = endTime2 - startTime2;
    
    Debug.log('AICoreSystemNodesDemo', `重复注册测试: 耗时${duplicateRegistrationTime}ms (应该很快，因为会跳过)`);
  }

  /**
   * 演示节点使用
   */
  private static async demonstrateNodeUsage(): Promise<void> {
    Debug.log('AICoreSystemNodesDemo', '=== 3. 节点使用演示 ===');
    
    // 演示深度学习节点
    this.demonstrateDeepLearningNodes();
    
    // 演示机器学习节点
    this.demonstrateMachineLearningNodes();
    
    // 演示AI工具节点
    this.demonstrateAIToolNodes();
    
    // 演示AI服务节点
    this.demonstrateAIServiceNodes();
    
    // 演示计算机视觉节点
    this.demonstrateComputerVisionNodes();
  }

  /**
   * 演示深度学习节点
   */
  private static demonstrateDeepLearningNodes(): void {
    Debug.log('AICoreSystemNodesDemo', '--- 深度学习节点演示 ---');
    
    const deepLearningNodeTypes = [
      AI_CORE_SYSTEM_NODE_TYPES.DEEP_LEARNING_MODEL,
      AI_CORE_SYSTEM_NODE_TYPES.NEURAL_NETWORK,
      AI_CORE_SYSTEM_NODE_TYPES.TRANSFORMER_MODEL,
      AI_CORE_SYSTEM_NODE_TYPES.GAN_MODEL,
      AI_CORE_SYSTEM_NODE_TYPES.VAE_MODEL
    ];
    
    deepLearningNodeTypes.forEach(nodeType => {
      const nodeInfo = NodeRegistry.getNode(nodeType);
      if (nodeInfo) {
        Debug.log('AICoreSystemNodesDemo', `✓ ${nodeInfo.name} (${nodeType}): ${nodeInfo.description}`);
        
        // 创建节点实例
        const nodeInstance = NodeRegistry.createNode(nodeType);
        if (nodeInstance) {
          Debug.log('AICoreSystemNodesDemo', `  实例创建成功: ${nodeInstance.constructor.name}`);
        }
      } else {
        Debug.warn('AICoreSystemNodesDemo', `✗ 节点未找到: ${nodeType}`);
      }
    });
  }

  /**
   * 演示机器学习节点
   */
  private static demonstrateMachineLearningNodes(): void {
    Debug.log('AICoreSystemNodesDemo', '--- 机器学习节点演示 ---');
    
    const mlNodeTypes = [
      AI_CORE_SYSTEM_NODE_TYPES.REINFORCEMENT_LEARNING,
      AI_CORE_SYSTEM_NODE_TYPES.FEDERATED_LEARNING,
      AI_CORE_SYSTEM_NODE_TYPES.TRANSFER_LEARNING,
      AI_CORE_SYSTEM_NODE_TYPES.MODEL_ENSEMBLE,
      AI_CORE_SYSTEM_NODE_TYPES.HYPERPARAMETER_TUNING
    ];
    
    mlNodeTypes.forEach(nodeType => {
      const nodeInfo = NodeRegistry.getNode(nodeType);
      if (nodeInfo) {
        Debug.log('AICoreSystemNodesDemo', `✓ ${nodeInfo.name} (${nodeType}): ${nodeInfo.description}`);
      } else {
        Debug.warn('AICoreSystemNodesDemo', `✗ 节点未找到: ${nodeType}`);
      }
    });
  }

  /**
   * 演示AI工具节点
   */
  private static demonstrateAIToolNodes(): void {
    Debug.log('AICoreSystemNodesDemo', '--- AI工具节点演示 ---');
    
    const aiToolNodeTypes = [
      AI_CORE_SYSTEM_NODE_TYPES.MODEL_DEPLOYMENT,
      AI_CORE_SYSTEM_NODE_TYPES.MODEL_MONITORING,
      AI_CORE_SYSTEM_NODE_TYPES.AUTO_ML,
      AI_CORE_SYSTEM_NODE_TYPES.EXPLAINABLE_AI,
      AI_CORE_SYSTEM_NODE_TYPES.MODEL_COMPRESSION
    ];
    
    aiToolNodeTypes.forEach(nodeType => {
      const nodeInfo = NodeRegistry.getNode(nodeType);
      if (nodeInfo) {
        Debug.log('AICoreSystemNodesDemo', `✓ ${nodeInfo.name} (${nodeType}): ${nodeInfo.description}`);
      } else {
        Debug.warn('AICoreSystemNodesDemo', `✗ 节点未找到: ${nodeType}`);
      }
    });
  }

  /**
   * 演示AI服务节点
   */
  private static demonstrateAIServiceNodes(): void {
    Debug.log('AICoreSystemNodesDemo', '--- AI服务节点演示 ---');
    
    const aiServiceNodeTypes = [
      AI_CORE_SYSTEM_NODE_TYPES.AI_INFERENCE,
      AI_CORE_SYSTEM_NODE_TYPES.AI_TRAINING,
      AI_CORE_SYSTEM_NODE_TYPES.NLP_PROCESSING,
      AI_CORE_SYSTEM_NODE_TYPES.SPEECH_RECOGNITION,
      AI_CORE_SYSTEM_NODE_TYPES.CHATBOT
    ];
    
    aiServiceNodeTypes.forEach(nodeType => {
      const nodeInfo = NodeRegistry.getNode(nodeType);
      if (nodeInfo) {
        Debug.log('AICoreSystemNodesDemo', `✓ ${nodeInfo.name} (${nodeType}): ${nodeInfo.description}`);
      } else {
        Debug.warn('AICoreSystemNodesDemo', `✗ 节点未找到: ${nodeType}`);
      }
    });
  }

  /**
   * 演示计算机视觉节点
   */
  private static demonstrateComputerVisionNodes(): void {
    Debug.log('AICoreSystemNodesDemo', '--- 计算机视觉节点演示 ---');
    
    const cvNodeTypes = [
      AI_CORE_SYSTEM_NODE_TYPES.IMAGE_SEGMENTATION,
      AI_CORE_SYSTEM_NODE_TYPES.OBJECT_DETECTION,
      AI_CORE_SYSTEM_NODE_TYPES.FACE_RECOGNITION,
      AI_CORE_SYSTEM_NODE_TYPES.IMAGE_GENERATION,
      AI_CORE_SYSTEM_NODE_TYPES.STYLE_TRANSFER
    ];
    
    cvNodeTypes.forEach(nodeType => {
      const nodeInfo = NodeRegistry.getNode(nodeType);
      if (nodeInfo) {
        Debug.log('AICoreSystemNodesDemo', `✓ ${nodeInfo.name} (${nodeType}): ${nodeInfo.description}`);
      } else {
        Debug.warn('AICoreSystemNodesDemo', `✗ 节点未找到: ${nodeType}`);
      }
    });
  }

  /**
   * 演示统计信息
   */
  private static async demonstrateStatistics(): Promise<void> {
    Debug.log('AICoreSystemNodesDemo', '=== 4. 统计信息演示 ===');
    
    const stats = aiCoreSystemNodesRegistry.getNodeCategoryStats();
    Debug.log('AICoreSystemNodesDemo', '节点分类统计:');
    Debug.log('AICoreSystemNodesDemo', `  深度学习节点: ${stats.deepLearning}个`);
    Debug.log('AICoreSystemNodesDemo', `  机器学习节点: ${stats.machineLearning}个`);
    Debug.log('AICoreSystemNodesDemo', `  AI工具节点: ${stats.aiTools}个`);
    Debug.log('AICoreSystemNodesDemo', `  AI服务节点: ${stats.aiServices}个`);
    Debug.log('AICoreSystemNodesDemo', `  计算机视觉节点: ${stats.computerVision}个`);
    
    const totalNodes = Object.values(stats).reduce((sum, count) => sum + count, 0);
    Debug.log('AICoreSystemNodesDemo', `  总计: ${totalNodes}个节点`);
  }

  /**
   * 演示分类查询
   */
  private static async demonstrateCategoryQueries(): Promise<void> {
    Debug.log('AICoreSystemNodesDemo', '=== 5. 分类查询演示 ===');
    
    // 搜索AI相关节点
    const aiNodes = NodeRegistry.searchNodes('ai');
    Debug.log('AICoreSystemNodesDemo', `搜索"ai"找到 ${aiNodes.length} 个节点`);
    
    // 搜索深度学习节点
    const deepLearningNodes = NodeRegistry.searchNodes('深度学习');
    Debug.log('AICoreSystemNodesDemo', `搜索"深度学习"找到 ${deepLearningNodes.length} 个节点`);
    
    // 搜索机器学习节点
    const machineLearningNodes = NodeRegistry.searchNodes('machine learning');
    Debug.log('AICoreSystemNodesDemo', `搜索"machine learning"找到 ${machineLearningNodes.length} 个节点`);
    
    // 搜索计算机视觉节点
    const visionNodes = NodeRegistry.searchNodes('vision');
    Debug.log('AICoreSystemNodesDemo', `搜索"vision"找到 ${visionNodes.length} 个节点`);
  }
}

/**
 * 运行演示的便捷函数
 */
export async function runAICoreSystemNodesDemo(): Promise<void> {
  await AICoreSystemNodesDemo.runDemo();
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  runAICoreSystemNodesDemo().catch(error => {
    console.error('演示运行失败:', error);
    process.exit(1);
  });
}
