/**
 * AI核心系统节点注册表测试
 */

import { AICoreSystemNodesRegistry, aiCoreSystemNodesRegistry, AI_CORE_SYSTEM_NODE_TYPES } from '../AICoreSystemNodesRegistry';
import { NodeRegistry } from '../NodeRegistry';

describe('AICoreSystemNodesRegistry', () => {
  let registry: AICoreSystemNodesRegistry;
  let nodeRegistry: NodeRegistry;

  beforeEach(() => {
    registry = AICoreSystemNodesRegistry.getInstance();
    nodeRegistry = NodeRegistry;
    
    // 重置注册状态
    registry.resetRegistration();
  });

  describe('单例模式', () => {
    test('应该返回同一个实例', () => {
      const instance1 = AICoreSystemNodesRegistry.getInstance();
      const instance2 = AICoreSystemNodesRegistry.getInstance();
      expect(instance1).toBe(instance2);
    });

    test('导出的实例应该是同一个', () => {
      const instance = AICoreSystemNodesRegistry.getInstance();
      expect(aiCoreSystemNodesRegistry).toBe(instance);
    });
  });

  describe('节点注册', () => {
    test('应该成功注册所有60个AI核心系统节点', () => {
      expect(registry.isRegistered()).toBe(false);
      expect(registry.getRegisteredNodeCount()).toBe(0);

      registry.registerAllNodes();

      expect(registry.isRegistered()).toBe(true);
      expect(registry.getRegisteredNodeCount()).toBe(60);
    });

    test('重复注册应该被跳过', () => {
      registry.registerAllNodes();
      const firstRegistration = registry.isRegistered();

      // 再次注册
      registry.registerAllNodes();
      
      expect(registry.isRegistered()).toBe(firstRegistration);
      expect(registry.getRegisteredNodeCount()).toBe(60);
    });

    test('应该正确注册深度学习节点', () => {
      registry.registerAllNodes();

      // 检查深度学习节点是否注册
      expect(nodeRegistry.getNode('DeepLearningModel')).toBeDefined();
      expect(nodeRegistry.getNode('NeuralNetwork')).toBeDefined();
      expect(nodeRegistry.getNode('ConvolutionalNetwork')).toBeDefined();
      expect(nodeRegistry.getNode('RecurrentNetwork')).toBeDefined();
      expect(nodeRegistry.getNode('TransformerModel')).toBeDefined();
      expect(nodeRegistry.getNode('GANModel')).toBeDefined();
      expect(nodeRegistry.getNode('VAEModel')).toBeDefined();
      expect(nodeRegistry.getNode('AttentionMechanism')).toBeDefined();
      expect(nodeRegistry.getNode('EmbeddingLayer')).toBeDefined();
      expect(nodeRegistry.getNode('DropoutLayer')).toBeDefined();
      expect(nodeRegistry.getNode('BatchNormalization')).toBeDefined();
      expect(nodeRegistry.getNode('ActivationFunction')).toBeDefined();
      expect(nodeRegistry.getNode('LossFunction')).toBeDefined();
      expect(nodeRegistry.getNode('Optimizer')).toBeDefined();
      expect(nodeRegistry.getNode('Regularization')).toBeDefined();
    });

    test('应该正确注册机器学习节点', () => {
      registry.registerAllNodes();

      // 检查机器学习节点是否注册
      expect(nodeRegistry.getNode('ReinforcementLearning')).toBeDefined();
      expect(nodeRegistry.getNode('FederatedLearning')).toBeDefined();
      expect(nodeRegistry.getNode('TransferLearning')).toBeDefined();
      expect(nodeRegistry.getNode('ModelEnsemble')).toBeDefined();
      expect(nodeRegistry.getNode('HyperparameterTuning')).toBeDefined();
      expect(nodeRegistry.getNode('ModelValidation')).toBeDefined();
      expect(nodeRegistry.getNode('CrossValidation')).toBeDefined();
      expect(nodeRegistry.getNode('FeatureSelection')).toBeDefined();
      expect(nodeRegistry.getNode('DimensionalityReduction')).toBeDefined();
      expect(nodeRegistry.getNode('Clustering')).toBeDefined();
    });

    test('应该正确注册AI工具节点', () => {
      registry.registerAllNodes();

      // 检查AI工具节点是否注册
      expect(nodeRegistry.getNode('ModelDeployment')).toBeDefined();
      expect(nodeRegistry.getNode('ModelMonitoring')).toBeDefined();
      expect(nodeRegistry.getNode('ModelVersioning')).toBeDefined();
      expect(nodeRegistry.getNode('AutoML')).toBeDefined();
      expect(nodeRegistry.getNode('ExplainableAI')).toBeDefined();
      expect(nodeRegistry.getNode('AIEthics')).toBeDefined();
      expect(nodeRegistry.getNode('ModelCompression')).toBeDefined();
      expect(nodeRegistry.getNode('Quantization')).toBeDefined();
      expect(nodeRegistry.getNode('Pruning')).toBeDefined();
      expect(nodeRegistry.getNode('Distillation')).toBeDefined();
    });

    test('应该正确注册AI服务节点', () => {
      registry.registerAllNodes();

      // 检查AI服务节点是否注册
      expect(nodeRegistry.getNode('AIModelLoad')).toBeDefined();
      expect(nodeRegistry.getNode('AIInference')).toBeDefined();
      expect(nodeRegistry.getNode('AITraining')).toBeDefined();
      expect(nodeRegistry.getNode('NLPProcessing')).toBeDefined();
      expect(nodeRegistry.getNode('ComputerVision')).toBeDefined();
      expect(nodeRegistry.getNode('SpeechRecognition')).toBeDefined();
      expect(nodeRegistry.getNode('SentimentAnalysis')).toBeDefined();
      expect(nodeRegistry.getNode('Recommendation')).toBeDefined();
      expect(nodeRegistry.getNode('Chatbot')).toBeDefined();
      expect(nodeRegistry.getNode('AIOptimization')).toBeDefined();
      expect(nodeRegistry.getNode('AIMonitoring')).toBeDefined();
      expect(nodeRegistry.getNode('AIModelVersion')).toBeDefined();
      expect(nodeRegistry.getNode('AIDataPreprocessing')).toBeDefined();
      expect(nodeRegistry.getNode('AIResultPostprocessing')).toBeDefined();
      expect(nodeRegistry.getNode('AIPerformance')).toBeDefined();
    });

    test('应该正确注册计算机视觉节点', () => {
      registry.registerAllNodes();

      // 检查计算机视觉节点是否注册
      expect(nodeRegistry.getNode('ImageSegmentation')).toBeDefined();
      expect(nodeRegistry.getNode('ObjectTracking')).toBeDefined();
      expect(nodeRegistry.getNode('FaceRecognition')).toBeDefined();
      expect(nodeRegistry.getNode('OpticalCharacterRecognition')).toBeDefined();
      expect(nodeRegistry.getNode('ImageGeneration')).toBeDefined();
      expect(nodeRegistry.getNode('StyleTransfer')).toBeDefined();
      expect(nodeRegistry.getNode('ImageEnhancement')).toBeDefined();
      expect(nodeRegistry.getNode('AugmentedReality')).toBeDefined();
      expect(nodeRegistry.getNode('ObjectDetection')).toBeDefined();
      expect(nodeRegistry.getNode('ImageClassification')).toBeDefined();
    });
  });

  describe('统计信息', () => {
    test('应该返回正确的节点分类统计', () => {
      const stats = registry.getNodeCategoryStats();
      
      expect(stats.deepLearning).toBe(15);
      expect(stats.machineLearning).toBe(10);
      expect(stats.aiTools).toBe(10);
      expect(stats.aiServices).toBe(15);
      expect(stats.computerVision).toBe(10);
    });

    test('注册前后节点数量应该正确', () => {
      expect(registry.getRegisteredNodeCount()).toBe(0);
      
      registry.registerAllNodes();
      
      expect(registry.getRegisteredNodeCount()).toBe(60);
    });
  });

  describe('节点类型常量', () => {
    test('应该包含所有节点类型常量', () => {
      // 深度学习节点常量
      expect(AI_CORE_SYSTEM_NODE_TYPES.DEEP_LEARNING_MODEL).toBe('DeepLearningModel');
      expect(AI_CORE_SYSTEM_NODE_TYPES.NEURAL_NETWORK).toBe('NeuralNetwork');
      expect(AI_CORE_SYSTEM_NODE_TYPES.TRANSFORMER_MODEL).toBe('TransformerModel');
      
      // 机器学习节点常量
      expect(AI_CORE_SYSTEM_NODE_TYPES.REINFORCEMENT_LEARNING).toBe('ReinforcementLearning');
      expect(AI_CORE_SYSTEM_NODE_TYPES.FEDERATED_LEARNING).toBe('FederatedLearning');
      
      // AI工具节点常量
      expect(AI_CORE_SYSTEM_NODE_TYPES.MODEL_DEPLOYMENT).toBe('ModelDeployment');
      expect(AI_CORE_SYSTEM_NODE_TYPES.AUTO_ML).toBe('AutoML');
      
      // AI服务节点常量
      expect(AI_CORE_SYSTEM_NODE_TYPES.AI_INFERENCE).toBe('AIInference');
      expect(AI_CORE_SYSTEM_NODE_TYPES.NLP_PROCESSING).toBe('NLPProcessing');
      
      // 计算机视觉节点常量
      expect(AI_CORE_SYSTEM_NODE_TYPES.IMAGE_SEGMENTATION).toBe('ImageSegmentation');
      expect(AI_CORE_SYSTEM_NODE_TYPES.OBJECT_DETECTION).toBe('ObjectDetection');
    });
  });

  describe('错误处理', () => {
    test('注册过程中的错误应该被正确处理', () => {
      // 模拟注册错误
      const originalRegisterNode = nodeRegistry.registerNode;
      nodeRegistry.registerNode = jest.fn().mockImplementation(() => {
        throw new Error('注册失败');
      });

      expect(() => {
        registry.registerAllNodes();
      }).toThrow('注册失败');

      // 恢复原始方法
      nodeRegistry.registerNode = originalRegisterNode;
    });
  });

  describe('重置功能', () => {
    test('重置后应该可以重新注册', () => {
      registry.registerAllNodes();
      expect(registry.isRegistered()).toBe(true);

      registry.resetRegistration();
      expect(registry.isRegistered()).toBe(false);
      expect(registry.getRegisteredNodeCount()).toBe(0);

      registry.registerAllNodes();
      expect(registry.isRegistered()).toBe(true);
      expect(registry.getRegisteredNodeCount()).toBe(60);
    });
  });
});
