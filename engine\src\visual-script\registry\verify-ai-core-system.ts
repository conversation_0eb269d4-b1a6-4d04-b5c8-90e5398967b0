/**
 * AI核心系统节点注册表验证脚本
 * 用于验证注册表是否正常工作
 */

import { aiCoreSystemNodesRegistry, AI_CORE_SYSTEM_NODE_TYPES } from './AICoreSystemNodesRegistry';

/**
 * 验证AI核心系统节点注册表
 */
async function verifyAICoreSystemNodesRegistry(): Promise<void> {
  console.log('🚀 开始验证AI核心系统节点注册表...\n');

  try {
    // 1. 检查初始状态
    console.log('📋 1. 检查初始状态');
    console.log(`   注册状态: ${aiCoreSystemNodesRegistry.isRegistered()}`);
    console.log(`   节点数量: ${aiCoreSystemNodesRegistry.getRegisteredNodeCount()}\n`);

    // 2. 执行注册
    console.log('📝 2. 执行节点注册');
    const startTime = Date.now();
    aiCoreSystemNodesRegistry.registerAllNodes();
    const endTime = Date.now();
    console.log(`   注册完成，耗时: ${endTime - startTime}ms`);
    console.log(`   注册状态: ${aiCoreSystemNodesRegistry.isRegistered()}`);
    console.log(`   节点数量: ${aiCoreSystemNodesRegistry.getRegisteredNodeCount()}\n`);

    // 3. 验证节点类型常量
    console.log('🔍 3. 验证节点类型常量');
    const nodeTypeKeys = Object.keys(AI_CORE_SYSTEM_NODE_TYPES);
    console.log(`   节点类型常量数量: ${nodeTypeKeys.length}`);
    console.log(`   示例常量: ${nodeTypeKeys.slice(0, 5).join(', ')}\n`);

    // 4. 验证统计信息
    console.log('📊 4. 验证统计信息');
    const stats = aiCoreSystemNodesRegistry.getNodeCategoryStats();
    console.log(`   深度学习节点: ${stats.deepLearning}个`);
    console.log(`   机器学习节点: ${stats.machineLearning}个`);
    console.log(`   AI工具节点: ${stats.aiTools}个`);
    console.log(`   AI服务节点: ${stats.aiServices}个`);
    console.log(`   计算机视觉节点: ${stats.computerVision}个`);
    
    const totalNodes = Object.values(stats).reduce((sum, count) => sum + count, 0);
    console.log(`   总计: ${totalNodes}个节点\n`);

    // 5. 验证重复注册
    console.log('🔄 5. 验证重复注册保护');
    const startTime2 = Date.now();
    aiCoreSystemNodesRegistry.registerAllNodes();
    const endTime2 = Date.now();
    console.log(`   重复注册耗时: ${endTime2 - startTime2}ms (应该很快)\n`);

    // 6. 验证重置功能
    console.log('🔧 6. 验证重置功能');
    aiCoreSystemNodesRegistry.resetRegistration();
    console.log(`   重置后状态: ${aiCoreSystemNodesRegistry.isRegistered()}`);
    console.log(`   重置后节点数: ${aiCoreSystemNodesRegistry.getRegisteredNodeCount()}`);
    
    // 重新注册
    aiCoreSystemNodesRegistry.registerAllNodes();
    console.log(`   重新注册后状态: ${aiCoreSystemNodesRegistry.isRegistered()}`);
    console.log(`   重新注册后节点数: ${aiCoreSystemNodesRegistry.getRegisteredNodeCount()}\n`);

    console.log('✅ AI核心系统节点注册表验证完成！');
    console.log('🎉 所有功能正常工作');

  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
    throw error;
  }
}

// 如果直接运行此文件，则执行验证
if (require.main === module) {
  verifyAICoreSystemNodesRegistry().catch(error => {
    console.error('验证失败:', error);
    process.exit(1);
  });
}

export { verifyAICoreSystemNodesRegistry };
