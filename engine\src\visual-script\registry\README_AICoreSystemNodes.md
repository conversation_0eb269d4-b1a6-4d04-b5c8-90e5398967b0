# AI核心系统节点注册表

## 概述

AI核心系统节点注册表是DL引擎视觉脚本系统的重要组成部分，负责注册和管理60个AI核心系统节点。这些节点涵盖了深度学习、机器学习、AI工具、AI服务和计算机视觉等五个主要领域。

## 节点分类

### 1. 深度学习节点（15个）
- **DeepLearningModel**: 深度学习模型
- **NeuralNetwork**: 神经网络
- **ConvolutionalNetwork**: 卷积神经网络
- **RecurrentNetwork**: 循环神经网络
- **TransformerModel**: Transformer模型
- **GANModel**: 生成对抗网络
- **VAEModel**: 变分自编码器
- **AttentionMechanism**: 注意力机制
- **EmbeddingLayer**: 嵌入层
- **DropoutLayer**: Dropout层
- **BatchNormalization**: 批量归一化
- **ActivationFunction**: 激活函数
- **LossFunction**: 损失函数
- **Optimizer**: 优化器
- **Regularization**: 正则化

### 2. 机器学习节点（10个）
- **ReinforcementLearning**: 强化学习
- **FederatedLearning**: 联邦学习
- **TransferLearning**: 迁移学习
- **ModelEnsemble**: 模型集成
- **HyperparameterTuning**: 超参数调优
- **ModelValidation**: 模型验证
- **CrossValidation**: 交叉验证
- **FeatureSelection**: 特征选择
- **DimensionalityReduction**: 降维
- **Clustering**: 聚类

### 3. AI工具节点（10个）
- **ModelDeployment**: 模型部署
- **ModelMonitoring**: 模型监控
- **ModelVersioning**: 模型版本管理
- **AutoML**: 自动机器学习
- **ExplainableAI**: 可解释AI
- **AIEthics**: AI伦理
- **ModelCompression**: 模型压缩
- **Quantization**: 量化
- **Pruning**: 剪枝
- **Distillation**: 知识蒸馏

### 4. AI服务节点（15个）
- **AIModelLoad**: AI模型加载
- **AIInference**: AI推理
- **AITraining**: AI训练
- **NLPProcessing**: 自然语言处理
- **ComputerVision**: 计算机视觉
- **SpeechRecognition**: 语音识别
- **SentimentAnalysis**: 情感分析
- **Recommendation**: 推荐系统
- **Chatbot**: 聊天机器人
- **AIOptimization**: AI优化
- **AIMonitoring**: AI监控
- **AIModelVersion**: AI模型版本
- **AIDataPreprocessing**: AI数据预处理
- **AIResultPostprocessing**: AI结果后处理
- **AIPerformance**: AI性能分析

### 5. 计算机视觉节点（10个）
- **ImageSegmentation**: 图像分割
- **ObjectTracking**: 目标跟踪
- **FaceRecognition**: 人脸识别
- **OpticalCharacterRecognition**: 光学字符识别
- **ImageGeneration**: 图像生成
- **StyleTransfer**: 风格迁移
- **ImageEnhancement**: 图像增强
- **AugmentedReality**: 增强现实
- **ObjectDetection**: 目标检测
- **ImageClassification**: 图像分类

## 使用方法

### 基本使用

```typescript
import { aiCoreSystemNodesRegistry } from './AICoreSystemNodesRegistry';

// 注册所有AI核心系统节点
aiCoreSystemNodesRegistry.registerAllNodes();

// 检查注册状态
console.log('已注册:', aiCoreSystemNodesRegistry.isRegistered());
console.log('节点数量:', aiCoreSystemNodesRegistry.getRegisteredNodeCount());
```

### 获取统计信息

```typescript
const stats = aiCoreSystemNodesRegistry.getNodeCategoryStats();
console.log('深度学习节点:', stats.deepLearning);
console.log('机器学习节点:', stats.machineLearning);
console.log('AI工具节点:', stats.aiTools);
console.log('AI服务节点:', stats.aiServices);
console.log('计算机视觉节点:', stats.computerVision);
```

### 使用节点类型常量

```typescript
import { AI_CORE_SYSTEM_NODE_TYPES } from './AICoreSystemNodesRegistry';
import { NodeRegistry } from './NodeRegistry';

// 获取节点信息
const nodeInfo = NodeRegistry.getNode(AI_CORE_SYSTEM_NODE_TYPES.DEEP_LEARNING_MODEL);

// 创建节点实例
const node = NodeRegistry.createNode(AI_CORE_SYSTEM_NODE_TYPES.AI_INFERENCE);
```

### 搜索和查找节点

```typescript
import { NodeRegistry } from './NodeRegistry';

// 搜索AI相关节点
const aiNodes = NodeRegistry.searchNodes('ai');

// 搜索深度学习节点
const deepLearningNodes = NodeRegistry.searchNodes('深度学习');

// 按分类获取节点
const aiServiceNodes = NodeRegistry.getNodesByCategory(NodeCategory.AI_SERVICES);
```

## 集成到编辑器

AI核心系统节点注册表已经集成到主注册系统中，会在系统初始化时自动注册所有节点。

```typescript
import { initializeVisualScriptSystem } from './registry';

// 初始化整个视觉脚本系统（包括AI核心系统节点）
await initializeVisualScriptSystem();
```

## 测试

运行测试以验证注册表功能：

```bash
npm test -- AICoreSystemNodesRegistry.test.ts
```

## 演示

运行演示以查看注册表的功能：

```typescript
import { runAICoreSystemNodesDemo } from './demo/AICoreSystemNodesDemo';

await runAICoreSystemNodesDemo();
```

## 开发指南

### 添加新的AI节点

1. 在相应的节点文件中实现节点类
2. 在注册表中添加节点注册代码
3. 更新节点类型常量
4. 添加相应的测试
5. 更新文档

### 节点分类规则

- **深度学习节点**: 使用 `NodeCategory.AI_DEEP_LEARNING`
- **机器学习节点**: 使用 `NodeCategory.AI_MACHINE_LEARNING`
- **AI工具节点**: 使用 `NodeCategory.AI_TOOLS`
- **AI服务节点**: 使用 `NodeCategory.AI_SERVICES`
- **计算机视觉节点**: 使用 `NodeCategory.AI_COMPUTER_VISION`

### 图标和颜色规范

- **深度学习**: 紫色 (#9C27B0)，图标使用 psychology、device_hub 等
- **机器学习**: 蓝色 (#3F51B5)，图标使用 psychology_alt、share 等
- **AI工具**: 橙红色 (#FF5722)，图标使用 cloud_upload、monitor 等
- **AI服务**: 绿色 (#4CAF50)，图标使用 download、psychology 等
- **计算机视觉**: 橙色 (#FF9800)，图标使用 crop、visibility 等

## 性能考虑

- 注册表使用单例模式，确保只有一个实例
- 支持重复注册检查，避免重复注册开销
- 使用懒加载，只在需要时注册节点
- 提供重置功能，便于测试和开发

## 故障排除

### 常见问题

1. **节点未找到**: 确保已调用 `registerAllNodes()` 方法
2. **重复注册警告**: 这是正常的，系统会自动跳过重复注册
3. **节点创建失败**: 检查节点类是否正确导入和实现

### 调试技巧

- 使用 `Debug.log` 查看注册过程
- 检查 `isRegistered()` 和 `getRegisteredNodeCount()` 状态
- 运行演示程序验证功能

## 更新日志

### v1.0.0 (2025-07-07)
- 初始版本发布
- 实现60个AI核心系统节点注册
- 支持5个主要AI分类
- 完整的测试覆盖
- 演示和文档完善
